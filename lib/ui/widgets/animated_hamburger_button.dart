import 'package:flutter/material.dart';

/// An animated button that transitions between hamburger (two lines) and close (X) icons
class AnimatedHamburgerButton extends StatefulWidget {
  /// Whether the button should show the close (X) state
  final bool isOpen;
  
  /// Callback when the button is pressed
  final VoidCallback onPressed;
  
  /// Color of the icon
  final Color? color;
  
  /// Size of the icon
  final double size;
  
  /// Duration of the animation
  final Duration animationDuration;

  /// Constructor
  const AnimatedHamburgerButton({
    super.key,
    required this.isOpen,
    required this.onPressed,
    this.color,
    this.size = 24.0,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedHamburgerButton> createState() => _AnimatedHamburgerButtonState();
}

class _AnimatedHamburgerButtonState extends State<AnimatedHamburgerButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Set initial state
    if (widget.isOpen) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedHamburgerButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpen != oldWidget.isOpen) {
      if (widget.isOpen) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: widget.onPressed,
      icon: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _HamburgerIconPainter(
              progress: _animation.value,
              color: widget.color ?? Theme.of(context).iconTheme.color ?? Colors.black,
            ),
          );
        },
      ),
    );
  }
}

/// Custom painter for the animated hamburger/close icon
class _HamburgerIconPainter extends CustomPainter {
  final double progress;
  final Color color;

  _HamburgerIconPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final width = size.width;
    final height = size.height;
    final centerX = width / 2;
    final centerY = height / 2;

    if (progress == 0.0) {
      // Draw hamburger menu (two horizontal lines)
      final lineLength = width * 0.6;
      final lineStart = (width - lineLength) / 2;

      // Top line
      canvas.drawLine(
        Offset(lineStart, centerY - 4),
        Offset(lineStart + lineLength, centerY - 4),
        paint,
      );

      // Bottom line
      canvas.drawLine(
        Offset(lineStart, centerY + 4),
        Offset(lineStart + lineLength, centerY + 4),
        paint,
      );
    } else if (progress == 1.0) {
      // Draw X (two diagonal lines)
      final lineLength = width * 0.5;
      final halfLength = lineLength / 2;

      // Top-left to bottom-right diagonal
      canvas.drawLine(
        Offset(centerX - halfLength, centerY - halfLength),
        Offset(centerX + halfLength, centerY + halfLength),
        paint,
      );

      // Top-right to bottom-left diagonal
      canvas.drawLine(
        Offset(centerX + halfLength, centerY - halfLength),
        Offset(centerX - halfLength, centerY + halfLength),
        paint,
      );
    } else {
      // Animate between hamburger and X - simplified rotation
      final lineLength = width * 0.6;
      final lineStart = (width - lineLength) / 2;
      final lineEnd = lineStart + lineLength;

      // Calculate the final diagonal positions for X
      final finalHalfLength = (width * 0.5) / 2;

      // Top line: rotate from horizontal to top-left/bottom-right diagonal
      final topStartX = lineStart + (centerX - finalHalfLength - lineStart) * progress;
      final topStartY = (centerY - 4) + (centerY - finalHalfLength - (centerY - 4)) * progress;
      final topEndX = lineEnd + (centerX + finalHalfLength - lineEnd) * progress;
      final topEndY = (centerY - 4) + (centerY + finalHalfLength - (centerY - 4)) * progress;

      canvas.drawLine(
        Offset(topStartX, topStartY),
        Offset(topEndX, topEndY),
        paint,
      );

      // Bottom line: rotate from horizontal to top-right/bottom-left diagonal
      final bottomStartX = lineStart + (centerX + finalHalfLength - lineStart) * progress;
      final bottomStartY = (centerY + 4) + (centerY - finalHalfLength - (centerY + 4)) * progress;
      final bottomEndX = lineEnd + (centerX - finalHalfLength - lineEnd) * progress;
      final bottomEndY = (centerY + 4) + (centerY + finalHalfLength - (centerY + 4)) * progress;

      canvas.drawLine(
        Offset(bottomStartX, bottomStartY),
        Offset(bottomEndX, bottomEndY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant _HamburgerIconPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
